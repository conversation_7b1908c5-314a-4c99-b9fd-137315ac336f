import { router } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
	Dimensions,
	FlatList,
	Image,
	NativeScrollEvent,
	NativeSyntheticEvent,
	Pressable,
	SafeAreaView,
	StatusBar,
	Text,
	View,
} from 'react-native';

const { width } = Dimensions.get('window');

interface OnboardingSlide {
	id: string;
	image: any;
	title: string;
	description: string;
}

const onboardingData: OnboardingSlide[] = [
	{
		id: '1',
		image: require('@/assets/images/slideOneImg.png'),
		title: 'Order anything you want on Camell',
		description:
			'Place orders in seconds, and get them delivered to your hostel—fast and secure.',
	},
	{
		id: '2',
		image: require('@/assets/images/slideTwoImg.png'),
		title: 'Affordable & fast campus delivery',
		description:
			'Place orders in seconds, and get them delivered to your hostel—fast and secure.',
	},
	{
		id: '3',
		image: require('@/assets/images/slideThreeImg.png'),
		title: 'Refer a friend & get paid',
		description:
			'Place orders in seconds, and get them delivered to your hostel—fast and secure.',
	},
];

export default function OnboardingScreen() {
	const [currentIndex, setCurrentIndex] = useState(0);
	const flatListRef = useRef<FlatList>(null);
	const autoPlayRef = useRef<ReturnType<typeof setInterval> | null>(null);

	// Auto-play functionality
	useEffect(() => {
		const startAutoPlay = () => {
			autoPlayRef.current = setInterval(() => {
				setCurrentIndex((prevIndex) => {
					const nextIndex = (prevIndex + 1) % onboardingData.length;
					flatListRef.current?.scrollToIndex({
						index: nextIndex,
						animated: true,
					});
					return nextIndex;
				});
			}, 3000); // Change slide every 3 seconds
		};

		startAutoPlay();

		return () => {
			if (autoPlayRef.current) {
				clearInterval(autoPlayRef.current);
			}
		};
	}, []);

	// Stop autoplay when user interacts
	const stopAutoPlay = () => {
		if (autoPlayRef.current) {
			clearInterval(autoPlayRef.current);
			autoPlayRef.current = null;
		}
	};

	const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
		stopAutoPlay();
		const contentOffsetX = event.nativeEvent.contentOffset.x;
		const index = Math.round(contentOffsetX / width);
		setCurrentIndex(index);
	};

	const handleGetStarted = () => {
		stopAutoPlay();
		router.replace('/auth/register');
	};

	const handleLogin = () => {
		stopAutoPlay();
		router.replace('/auth/login');
	};

	const renderSlide = ({ item }: { item: OnboardingSlide }) => (
		<View
			className='flex-1 items-center justify-center px-6'
			style={{ width }}>
			<Image
				source={item.image}
				className='w-80 h-80 mb-8'
				resizeMode='contain'
			/>
			<Text className='text-3xl font-bold text-secondary text-center mb-4'>
				{item.title}
			</Text>
			<Text className='text-grey-50 text-center text-base leading-6 px-4'>
				{item.description}
			</Text>
		</View>
	);

	const renderPagination = () => (
		<View className='flex-row justify-center w-screen items-center mb-8 mt-9'>
			{onboardingData.map((_, index) => (
				<View
					key={index}
					className={`h-1 rounded-full mx-1 ${
						index === currentIndex ? 'bg-primary w-12' : 'bg-grey-30 w-12'
					}`}
				/>
			))}
		</View>
	);

	return (
		<SafeAreaView className='flex-1 bg-bg-color'>
			<StatusBar
				barStyle='dark-content'
				backgroundColor='#FEFEFE'
			/>
			{/* Pagination at the top */}
			<View className='pt-8 w-screen'>{renderPagination()}</View>

			{/* Swipable Slides Content */}
			<View className='flex-1'>
				<FlatList
					ref={flatListRef}
					data={onboardingData}
					renderItem={renderSlide}
					horizontal
					pagingEnabled
					showsHorizontalScrollIndicator={false}
					onScroll={handleScroll}
					scrollEventThrottle={16}
					keyExtractor={(item) => item.id}
					onTouchStart={stopAutoPlay}
					onScrollBeginDrag={stopAutoPlay}
				/>
			</View>

			{/* Fixed Bottom Buttons */}
			<View className='px-6 pb-8 space-y-4'>
				<Pressable
					onPress={handleGetStarted}
					className='bg-primary py-4 rounded-lg w-full'>
					<Text className='text-white font-semibold text-center text-lg'>
						Get Started
					</Text>
				</Pressable>

				<Pressable
					onPress={handleLogin}
					className='py-4 w-full'>
					<Text className='text-primary font-medium text-center text-lg'>
						Login to Account
					</Text>
				</Pressable>
			</View>
		</SafeAreaView>
	);
}
